using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Data;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// Migration لإضافة حقول الرخص الجديدة للسائقين
    /// </summary>
    public static class AddDriverLicenseFields
    {
        /// <summary>
        /// تطبيق Migration لإضافة الحقول الجديدة
        /// </summary>
        public static async Task ApplyMigrationAsync(DbContext context)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إضافة حقول الرخص الجديدة للسائقين...");

                // التحقق من وجود الأعمدة أولاً
                var licenseExpiryExists = await CheckColumnExistsAsync(context, "Drivers", "LicenseExpiryDate");
                var drivingLicenseNumberExists = await CheckColumnExistsAsync(context, "Drivers", "DrivingLicenseNumber");
                var drivingLicenseExpiryExists = await CheckColumnExistsAsync(context, "Drivers", "DrivingLicenseExpiryDate");

                var sqlCommands = new List<string>();

                // إضافة عمود تاريخ انتهاء الرخصة
                if (!licenseExpiryExists)
                {
                    sqlCommands.Add("ALTER TABLE Drivers ADD LicenseExpiryDate datetime2 NULL;");
                    System.Diagnostics.Debug.WriteLine("✅ سيتم إضافة عمود LicenseExpiryDate");
                }

                // إضافة عمود رقم رخصة القيادة
                if (!drivingLicenseNumberExists)
                {
                    sqlCommands.Add("ALTER TABLE Drivers ADD DrivingLicenseNumber nvarchar(20) NULL;");
                    System.Diagnostics.Debug.WriteLine("✅ سيتم إضافة عمود DrivingLicenseNumber");
                }

                // إضافة عمود تاريخ انتهاء رخصة القيادة
                if (!drivingLicenseExpiryExists)
                {
                    sqlCommands.Add("ALTER TABLE Drivers ADD DrivingLicenseExpiryDate datetime2 NULL;");
                    System.Diagnostics.Debug.WriteLine("✅ سيتم إضافة عمود DrivingLicenseExpiryDate");
                }

                // تنفيذ الأوامر
                foreach (var sql in sqlCommands)
                {
                    await context.Database.ExecuteSqlRawAsync(sql);
                }

                if (sqlCommands.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم إضافة {sqlCommands.Count} عمود جديد بنجاح");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("✅ جميع الأعمدة موجودة مسبقاً");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إضافة حقول الرخص: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التحقق من وجود عمود في الجدول
        /// </summary>
        private static async Task<bool> CheckColumnExistsAsync(DbContext context, string tableName, string columnName)
        {
            try
            {
                var sql = $@"
                    SELECT COUNT(*)
                    FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_NAME = '{tableName}' AND COLUMN_NAME = '{columnName}'";

                var connection = context.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    await connection.OpenAsync();
                }

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                var result = await command.ExecuteScalarAsync();

                return Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التحقق من وجود العمود {columnName}: {ex.Message}");
                return false;
            }
        }
    }
}
